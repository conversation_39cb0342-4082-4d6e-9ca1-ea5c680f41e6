<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infographic: Trends in Space Lab SaaS Platforms</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- 
        Chosen Color Palette: "Brilliant Blues & Vibrant Oranges" inspired
        Primary Blue: #005f73
        Secondary Blue: #0a9396
        Accent Orange: #ee9b00
        Accent Yellow: #ffb703
        Neutral Background: bg-slate-100 (#f1f5f9)
        Card Background: bg-white
        Text: text-slate-800 (#1e293b)

        Confirmation: NEITHER Mermaid JS NOR SVG were used anywhere in this HTML output.
        All charts are rendered using Chart.js on HTML5 Canvas.
        Diagrams (like flowcharts) are constructed using HTML and Tailwind CSS.
        Icons are Unicode characters.
    -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px; /* Default max-width, can be overridden by Tailwind max-w classes */
            margin-left: auto;
            margin-right: auto;
            height: 320px; /* Default base height for mobile */
            max-height: 350px; /* Default max height */
        }
        @media (min-width: 768px) { /* md breakpoint */
            .chart-container {
                height: 350px;
                max-height: 400px;
            }
        }
        .sticky-nav {
            position: sticky;
            top: 0;
            z-index: 50;
        }
        .stat-card {
            background-color: #005f73; /* Primary Blue */
            color: white;
        }
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            line-height: 1;
        }
        .flowchart-step {
            border: 2px solid #0a9396; /* Secondary Blue */
            background-color: white;
            color: #1e293b;
        }
        .flowchart-arrow {
            font-size: 2rem;
            color: #ee9b00; /* Accent Orange */
        }
        .section-title-underline {
            border-bottom: 3px solid #ee9b00; /* Accent Orange */
            padding-bottom: 0.5rem;
            display: inline-block;
        }
    </style>
</head>
<body class="bg-slate-100 text-slate-800">

    <header class="bg-gradient-to-r from-[#005f73] to-[#0a9396] text-white p-8 text-center shadow-lg">
        <h1 class="text-4xl font-bold mb-2">The Expanding Frontier: Market Trends in Space-Based Life Science</h1>
        <p class="text-xl">Harnessing Lab-on-a-Chip Systems & SaaS Platforms for Breakthrough Discoveries on the ISS</p>
    </header>

    <nav class="sticky-nav bg-white shadow-md">
        <div class="container mx-auto px-4 py-3 flex flex-wrap justify-center space-x-4 text-sm md:text-base">
            <a href="#market-overview" class="text-[#005f73] hover:text-[#ee9b00] font-semibold">Market Overview</a>
            <a href="#enabling-tech" class="text-[#005f73] hover:text-[#ee9b00] font-semibold">Enabling Technologies</a>
            <a href="#data-revolution" class="text-[#005f73] hover:text-[#ee9b00] font-semibold">Data Revolution</a>
            <a href="#competitive-landscape" class="text-[#005f73] hover:text-[#ee9b00] font-semibold">Key Innovators</a>
            <a href="#future-outlook" class="text-[#005f73] hover:text-[#ee9b00] font-semibold">Future Outlook</a>
        </div>
    </nav>

    <main class="container mx-auto p-4 md:p-8">

        <section id="market-overview" class="mb-12 scroll-mt-20">
            <h2 class="text-3xl font-bold text-center mb-2 text-[#005f73]"><span class="section-title-underline">The Microgravity Research Market: A New Era</span></h2>
            <p class="text-center text-lg text-slate-700 mb-8 max-w-3xl mx-auto">
                The International Space Station (ISS) provides an unparalleled environment for life science research. Microgravity fundamentally alters biological processes, offering unique insights into areas like cancer, immunology, and organoid development. This section highlights the growing market driven by these opportunities.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Projected Market Growth (Illustrative)</h3>
                    <p class="text-sm text-slate-600 mb-4">The global market for space-based life science research and associated LOC/SaaS platforms is poised for significant expansion as capabilities mature and terrestrial applications become clearer.</p>
                    <div class="chart-container max-w-md h-[300px] md:h-[350px]">
                        <canvas id="marketGrowthChart"></canvas>
                    </div>
                     <p class="text-xs text-slate-500 mt-2 text-center">Illustrative data based on report emphasis on growth and opportunity.</p>
                </div>

                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Key Research Areas Driving Demand (2025 Focus)</h3>
                     <p class="text-sm text-slate-600 mb-4">Research in microgravity is diverse, with specific fields demonstrating high potential for breakthroughs and attracting significant investment.</p>
                    <div class="chart-container max-w-xs h-[300px] md:h-[350px]">
                        <canvas id="researchAreaChart"></canvas>
                    </div>
                    <p class="text-xs text-slate-500 mt-2 text-center">Illustrative market share based on report emphasis.</p>
                </div>
            </div>
            <div class="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="stat-card rounded-lg shadow-lg p-6 text-center">
                    <div class="text-4xl mb-2">🔬</div>
                    <h4 class="text-lg font-semibold mb-1">Cancer Biology</h4>
                    <p class="text-sm opacity-90">Enhanced 3D tumor models & drug discovery.</p>
                </div>
                <div class="stat-card rounded-lg shadow-lg p-6 text-center">
                    <div class="text-4xl mb-2">🛡️</div>
                    <h4 class="text-lg font-semibold mb-1">Immunology</h4>
                    <p class="text-sm opacity-90">Accelerated immune aging studies & therapies.</p>
                </div>
                <div class="stat-card rounded-lg shadow-lg p-6 text-center">
                     <div class="text-4xl mb-2">🦠</div>
                    <h4 class="text-lg font-semibold mb-1">Microbiology</h4>
                    <p class="text-sm opacity-90">Virulence changes & countermeasure development.</p>
                </div>
                <div class="stat-card rounded-lg shadow-lg p-6 text-center">
                    <div class="text-4xl mb-2">🧠</div>
                    <h4 class="text-lg font-semibold mb-1">Organoids & Tissues</h4>
                    <p class="text-sm opacity-90">Advanced disease modeling & regenerative medicine.</p>
                </div>
            </div>
        </section>

        <section id="enabling-tech" class="mb-12 scroll-mt-20">
            <h2 class="text-3xl font-bold text-center mb-2 text-[#005f73]"><span class="section-title-underline">Enabling Technologies: The LOC & SaaS Revolution</span></h2>
            <p class="text-center text-lg text-slate-700 mb-8 max-w-3xl mx-auto">
                Miniaturized Lab-on-a-Chip (LOC) systems and sophisticated Software-as-a-Service (SaaS) platforms are pivotal in realizing the potential of ISS research. Automation, integration, and intelligent data handling are key trends.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8 items-start">
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Technology Adoption in Space Research (Illustrative %)</h3>
                    <p class="text-sm text-slate-600 mb-4">The adoption of advanced technologies is rapidly increasing, enhancing experimental capabilities and data output.</p>
                    <div class="chart-container max-w-md h-[300px] md:h-[350px]">
                        <canvas id="techAdoptionChart"></canvas>
                    </div>
                    <p class="text-xs text-slate-500 mt-2 text-center">Illustrative adoption rates indicating trends.</p>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Key LOC Hardware Features</h3>
                    <ul class="space-y-3 text-slate-700">
                        <li class="flex items-center"><span class="text-2xl mr-3 text-[#ee9b00]">🤏</span> Miniaturization & Robustness for spaceflight.</li>
                        <li class="flex items-center"><span class="text-2xl mr-3 text-[#ee9b00]">🤖</span> High Automation to minimize crew intervention.</li>
                        <li class="flex items-center"><span class="text-2xl mr-3 text-[#ee9b00]">🛰️</span> Integrated Sensors for real-time environmental & biological monitoring.</li>
                        <li class="flex items-center"><span class="text-2xl mr-3 text-[#ee9b00]">🧩</span> Modular & Multi-Purpose designs for versatility.</li>
                        <li class="flex items-center"><span class="text-2xl mr-3 text-[#ee9b00]">🔬</span> On-Chip Analytics (microscopy, spectrometry).</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-xl p-6 md:col-span-2">
                <h3 class="text-xl font-semibold mb-6 text-center text-[#0a9396]">The SaaS-Powered Research Lifecycle on ISS</h3>
                <p class="text-sm text-slate-600 mb-6 text-center max-w-2xl mx-auto">
                    Modern SaaS platforms orchestrate the entire research workflow, from meticulous planning to collaborative discovery, transforming how science is conducted in orbit.
                </p>
                <div class="flex flex-col md:flex-row items-center justify-around space-y-4 md:space-y-0 md:space-x-2">
                    <div class="flowchart-step rounded-lg p-4 w-full md:w-1/5 text-center shadow-md">
                        <div class="text-3xl mb-2">📝</div>
                        <h4 class="font-semibold">1. Design & Plan</h4>
                        <p class="text-xs">Protocols, Hardware Config, Timelines</p>
                    </div>
                    <div class="flowchart-arrow transform md:rotate-0">➔</div>
                    <div class="flowchart-step rounded-lg p-4 w-full md:w-1/5 text-center shadow-md">
                        <div class="text-3xl mb-2">🛰️</div>
                        <h4 class="font-semibold">2. On-Orbit Execution</h4>
                        <p class="text-xs">Automated Control & Real-Time Monitoring</p>
                    </div>
                    <div class="flowchart-arrow transform md:rotate-0">➔</div>
                    <div class="flowchart-step rounded-lg p-4 w-full md:w-1/5 text-center shadow-md">
                        <div class="text-3xl mb-2">📊</div>
                        <h4 class="font-semibold">3. Data Acquisition</h4>
                        <p class="text-xs">Imaging, Sensors, 'Omics Sampling</p>
                    </div>
                    <div class="flowchart-arrow transform md:rotate-0">➔</div>
                    <div class="flowchart-step rounded-lg p-4 w-full md:w-1/5 text-center shadow-md">
                        <div class="text-3xl mb-2">🧬</div>
                        <h4 class="font-semibold">4. Analysis & Insights</h4>
                        <p class="text-xs">Bioinformatics, AI/ML, Visualization</p>
                    </div>
                     <div class="flowchart-arrow transform md:rotate-0">➔</div>
                    <div class="flowchart-step rounded-lg p-4 w-full md:w-1/5 text-center shadow-md">
                        <div class="text-3xl mb-2">🌍</div>
                        <h4 class="font-semibold">5. Collaboration & Publication</h4>
                        <p class="text-xs">FAIR Data, Reporting, Discovery Sharing</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="data-revolution" class="mb-12 scroll-mt-20">
            <h2 class="text-3xl font-bold text-center mb-2 text-[#005f73]"><span class="section-title-underline">The Data Revolution in Space Biology</span></h2>
             <p class="text-center text-lg text-slate-700 mb-8 max-w-3xl mx-auto">
                Space experiments are generating an unprecedented volume and variety of biological data, especially from 'omics technologies. Advanced analytics, including AI/ML, are becoming indispensable for extracting meaningful knowledge.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Growth in 'Omics Data Generation (Illustrative)</h3>
                    <p class="text-sm text-slate-600 mb-4">The types and volume of 'omics data (genomics, transcriptomics, proteomics, etc.) collected from space experiments are rapidly expanding.</p>
                    <div class="chart-container max-w-md h-[300px] md:h-[350px]">
                        <canvas id="omicsDataChart"></canvas>
                    </div>
                    <p class="text-xs text-slate-500 mt-2 text-center">Illustrative data showing diversity and growth trend.</p>
                </div>
                 <div class="bg-white rounded-lg shadow-xl p-6 flex flex-col justify-center items-center text-center h-full">
                    <div class="text-[#ee9b00] text-6xl mb-4">📈</div>
                    <h3 class="text-2xl font-semibold mb-3 text-[#0a9396]">AI/ML: Deciphering Complexity</h3>
                    <p class="text-slate-700 mb-2">Artificial Intelligence and Machine Learning are crucial for:</p>
                    <ul class="text-left text-slate-600 space-y-1 list-disc list-inside">
                        <li>Pattern recognition in complex datasets</li>
                        <li>Predictive modeling of biological responses</li>
                        <li>Automated anomaly detection</li>
                        <li>Advancing multi-modal data integration</li>
                    </ul>
                    <div class="stat-card rounded-lg shadow-lg p-4 mt-6 w-full max-w-xs">
                        <p class="stat-number text-white">10x</p>
                        <p class="text-sm opacity-90">Projected increase in data complexity requiring AI by 2030 (Illustrative)</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="competitive-landscape" class="mb-12 scroll-mt-20">
            <h2 class="text-3xl font-bold text-center mb-2 text-[#005f73]"><span class="section-title-underline">Key Innovators & Competitive Landscape</span></h2>
            <p class="text-center text-lg text-slate-700 mb-8 max-w-3xl mx-auto">
                A diverse ecosystem of space agencies, commercial companies, and research institutions are driving innovation in LOC hardware, SaaS platforms, and on-orbit research capabilities.
            </p>
             <div class="bg-white rounded-lg shadow-xl p-6">
                <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Key Player Positioning (Illustrative Impact & Specialization)</h3>
                <p class="text-sm text-slate-600 mb-4">Various organizations contribute specialized expertise and technologies to the space life sciences ecosystem. Bubble size indicates illustrative relative impact/investment.</p>
                <div class="chart-container max-w-full md:max-w-2xl lg:max-w-3xl h-[400px] md:h-[450px]">
                    <canvas id="keyPlayersChart"></canvas>
                </div>
                <p class="text-xs text-slate-500 mt-2 text-center">Illustrative data reflecting roles highlighted in the report.</p>
            </div>
        </section>

        <section id="future-outlook" class="mb-12 scroll-mt-20">
            <h2 class="text-3xl font-bold text-center mb-2 text-[#005f73]"><span class="section-title-underline">Future Outlook & Opportunities</span></h2>
            <p class="text-center text-lg text-slate-700 mb-8 max-w-3xl mx-auto">
                The future of space-based life science research is bright, with advancements poised to deliver significant terrestrial benefits and support long-duration human space exploration.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Forecasted Impact</h3>
                    <ul class="space-y-3 text-slate-700">
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#005f73]">💊</span> Development of novel drug therapies and disease models.</li>
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#005f73]">🌏</span> Deeper understanding of fundamental human biology with terrestrial applications.</li>
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#005f73]">🧑‍🚀</span> Enhanced astronaut health and performance for long-duration missions.</li>
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#005f73]">💡</span> Acceleration of scientific discovery through unique microgravity insights.</li>
                    </ul>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <h3 class="text-xl font-semibold mb-3 text-[#0a9396]">Emerging Trends to Watch</h3>
                     <ul class="space-y-3 text-slate-700">
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#ee9b00]">🤖</span> AI-driven adaptive experimentation for optimized on-orbit science.</li>
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#ee9b00]">🔬</span> Increased on-orbit sample processing and analysis capabilities.</li>
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#ee9b00]">🤝</span> Greater emphasis on standardized data (FAIR) and collaborative open science.</li>
                        <li class="flex items-start"><span class="text-2xl mr-3 text-[#ee9b00]">🛰️</span> Development of next-generation LOCs with even more integrated functionalities.</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="text-center py-10 bg-gradient-to-r from-[#005f73] to-[#0a9396] text-white rounded-lg shadow-xl">
            <h2 class="text-3xl font-bold mb-4">Pioneering the Next Scientific Revolution, In Orbit</h2>
            <p class="text-lg max-w-2xl mx-auto mb-6">
                The convergence of Lab-on-a-Chip technology, advanced SaaS platforms, and the unique microgravity environment of the ISS is setting the stage for transformative discoveries. The market for these solutions is not just growing; it's enabling a new paradigm in life science research.
            </p>
            <a href="#market-overview" class="bg-[#ee9b00] hover:bg-[#ffb703] text-slate-900 font-bold py-3 px-8 rounded-lg shadow-md transition duration-300">Explore the Trends</a>
        </section>
    </main>

    <footer class="text-center py-6 text-sm text-slate-600">
        <p>&copy; 2025 Space Life Science Analytics. Infographic based on internal research report.</p>
        <p>Data presented in charts is illustrative and based on qualitative trends in the source material.</p>
    </footer>

    <script>
        const primaryBlue = '#005f73';
        const secondaryBlue = '#0a9396';
        const accentOrange = '#ee9b00';
        const accentYellow = '#ffb703';
        const textGray = '#1e293b';
        const lightGray = '#f1f5f9';

        function wrapLabel(str, maxWidth) {
            if (str.length <= maxWidth) return str;
            const words = str.split(' ');
            let currentLine = '';
            const lines = [];
            for (const word of words) {
                if ((currentLine + word).length > maxWidth && currentLine.length > 0) {
                    lines.push(currentLine.trim());
                    currentLine = word + ' ';
                } else {
                    currentLine += word + ' ';
                }
            }
            lines.push(currentLine.trim());
            return lines;
        }
        
        const commonChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: textGray,
                        font: { size: 12 }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                              return label.join(' ');
                            } else {
                              return label;
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: { color: textGray, font: { size: 11 } },
                    grid: { color: 'rgba(0,0,0,0.05)' }
                },
                y: {
                    ticks: { color: textGray, font: { size: 11 } },
                    grid: { color: 'rgba(0,0,0,0.1)' },
                    beginAtZero: true
                }
            }
        };

        // Market Growth Chart
        const marketGrowthCtx = document.getElementById('marketGrowthChart').getContext('2d');
        new Chart(marketGrowthCtx, {
            type: 'line',
            data: {
                labels: ['2022', '2023', '2024', '2025 (E)', '2026 (P)', '2027 (P)'],
                datasets: [{
                    label: 'Market Size (USD Billions, Illustrative)',
                    data: [0.8, 1.2, 1.9, 2.8, 4.0, 5.5],
                    borderColor: primaryBlue,
                    backgroundColor: 'rgba(0, 95, 115, 0.1)',
                    fill: true,
                    tension: 0.3,
                    pointBackgroundColor: primaryBlue,
                    pointRadius: 4,
                }]
            },
            options: commonChartOptions
        });

        // Research Area Chart
        const researchAreaCtx = document.getElementById('researchAreaChart').getContext('2d');
        new Chart(researchAreaCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cancer Biology', 'Immunology', 'Organoid R&D', 'Microbiology', 'Other'],
                datasets: [{
                    label: 'Market Share by Research Area (Illustrative)',
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [primaryBlue, secondaryBlue, accentOrange, accentYellow, '#94a3b8'],
                    borderColor: lightGray,
                    borderWidth: 2,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom', labels: { color: textGray, font: {size: 11}, boxWidth: 15 } },
                    tooltip: commonChartOptions.plugins.tooltip
                }
            }
        });

        // Technology Adoption Chart
        const techAdoptionCtx = document.getElementById('techAdoptionChart').getContext('2d');
        new Chart(techAdoptionCtx, {
            type: 'bar',
            data: {
                labels: [wrapLabel('Miniaturized LOC Systems',16), wrapLabel('Automated On-Orbit Processes',16), wrapLabel('Integrated SaaS Platforms',16), wrapLabel('AI/ML in Data Analysis',16)],
                datasets: [{
                    label: 'Adoption Rate (%) in Space Life Science (Illustrative)',
                    data: [70, 60, 55, 40],
                    backgroundColor: [primaryBlue, secondaryBlue, accentOrange, accentYellow],
                    borderRadius: 4,
                    barPercentage: 0.7
                }]
            },
            options: { ...commonChartOptions, indexAxis: 'y', scales: { ...commonChartOptions.scales, y: { ...commonChartOptions.scales.y, ticks: { ...commonChartOptions.scales.y.ticks, autoSkip: false } } } }
        });
        
        // Omics Data Chart
        const omicsDataCtx = document.getElementById('omicsDataChart').getContext('2d');
        new Chart(omicsDataCtx, {
            type: 'bar', // Changed to stacked bar
            data: {
                labels: ['2021', '2023', '2025 (E)'],
                datasets: [
                    {
                        label: 'Transcriptomics',
                        data: [30, 45, 60],
                        backgroundColor: primaryBlue,
                    },
                    {
                        label: 'Proteomics',
                        data: [20, 30, 45],
                        backgroundColor: secondaryBlue,
                    },
                    {
                        label: 'Metabolomics',
                        data: [15, 25, 35],
                        backgroundColor: accentOrange,
                    },
                    {
                        label: wrapLabel('Single-Cell/Spatial Omics',16),
                        data: [5, 15, 30],
                        backgroundColor: accentYellow,
                    }
                ]
            },
            options: {
                ...commonChartOptions,
                scales: {
                    x: { ...commonChartOptions.scales.x, stacked: true },
                    y: { ...commonChartOptions.scales.y, stacked: true, title: { display: true, text: 'Relative Data Volume (Illustrative)', color: textGray } }
                },
                 plugins: {
                    legend: { position: 'top', labels: { color: textGray, font: {size: 11}, boxWidth: 15 } },
                    tooltip: commonChartOptions.plugins.tooltip
                }
            }
        });

        // Key Players Chart
        const keyPlayersCtx = document.getElementById('keyPlayersChart').getContext('2d');
        new Chart(keyPlayersCtx, {
            type: 'bubble',
            data: {
                datasets: [
                    {
                        label: 'NASA (Agency/GeneLab)',
                        data: [{ x: 15, y: 80, r: 25 }], // x: Specialization Score, y: Impact Score, r: Resource/Investment
                        backgroundColor: primaryBlue,  borderColor: primaryBlue,
                    },
                    {
                        label: 'ESA (Agency/Facilities)',
                        data: [{ x: 25, y: 75, r: 22 }],
                        backgroundColor: secondaryBlue, borderColor: secondaryBlue,
                    },
                    {
                        label: 'Space Tango (Commercial)',
                        data: [{ x: 60, y: 60, r: 18 }],
                        backgroundColor: accentOrange, borderColor: accentOrange,
                    },
                    {
                        label: 'BioServe (Academic/Commercial)',
                        data: [{ x: 50, y: 55, r: 15 }],
                        backgroundColor: accentYellow, borderColor: accentYellow,
                    },
                    {
                        label: 'SpacePharma (Commercial)',
                        data: [{ x: 70, y: 50, r: 16 }],
                        backgroundColor: '#64748b', borderColor: '#64748b', // slate-500
                    },
                     {
                        label: 'Research Institutes (e.g. Sanford, UCSD)',
                        data: [{ x: 35, y: 70, r: 20 }],
                        backgroundColor: '#e11d48', borderColor: '#e11d48', // rose-600
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: true, position: 'bottom', labels: {color: textGray, font: {size:10}, boxWidth:12} },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.7)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.x !== null) {
                                    label += `(Specialization: ${context.parsed.x}, Impact: ${context.parsed.y}, Resources: ${context.raw.r})`;
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: { display: true, text: 'Degree of Technology Specialization (Illustrative)', color: textGray, font:{size:10} },
                        min: 0, max: 100,
                        ticks: { color: textGray, font: {size:10} },
                        grid: { color: 'rgba(0,0,0,0.05)' }
                    },
                    y: {
                        title: { display: true, text: 'Overall Market Impact Score (Illustrative)', color: textGray, font:{size:10} },
                        min: 0, max: 100,
                        ticks: { color: textGray, font: {size:10} },
                        grid: { color: 'rgba(0,0,0,0.1)' }
                    }
                }
            }
        });

    </script>
</body>
</html>
